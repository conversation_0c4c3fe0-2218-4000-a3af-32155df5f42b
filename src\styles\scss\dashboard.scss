// 数据大屏专用样式
.dashboard-theme {
  // 全局背景
  background: linear-gradient(135deg, #0c1426 0%, #1a2332 100%);
  color: #ffffff;

  // 卡片样式
  .dashboard-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(14, 66, 210, 0.3);
    border-radius: 12px;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;

    &:hover {
      border-color: rgba(14, 66, 210, 0.6);
      box-shadow: 0 0 20px rgba(14, 66, 210, 0.4);
    }
  }

  // 标题样式
  .dashboard-section-title {
    font-size: 20px;
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 16px;
    position: relative;
    padding-left: 12px;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 20px;
      background: linear-gradient(180deg, #0e42d2 0%, #64a6ff 100%);
      border-radius: 2px;
    }
  }

  // 数据展示
  .dashboard-metric {
    .metric-value {
      font-size: 32px;
      font-weight: 700;
      color: #0e42d2;
      font-family: 'Courier New', monospace;
    }

    .metric-label {
      font-size: 14px;
      color: rgba(255, 255, 255, 0.7);
      margin-top: 4px;
    }

    .metric-trend {
      font-size: 12px;
      margin-top: 8px;

      &.up {
        color: #00d4aa;
      }

      &.down {
        color: #ff4757;
      }
    }
  }

  // 图表容器
  .dashboard-chart {
    height: 300px;
    padding: 16px;
  }

  // 动画效果
  .dashboard-animate {
    animation: dashboardFadeIn 0.6s ease-out;
  }

  @keyframes dashboardFadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  // 响应式
  @media (max-width: 1200px) {
    .dashboard-header {
      height: 60px;

      .dashboard-title {
        font-size: 24px;
      }
    }
  }
}

// CSS变量定义
:root {
  --dashboard-primary: #0e42d2;
  --dashboard-bg: linear-gradient(135deg, #0c1426 0%, #1a2332 100%);
  --dashboard-card-bg: rgba(255, 255, 255, 0.05);
  --dashboard-border: rgba(14, 66, 210, 0.3);
  --dashboard-text: #ffffff;
  --dashboard-text-secondary: rgba(255, 255, 255, 0.7);
  --dashboard-success: #00d4aa;
  --dashboard-warning: #ffb800;
  --dashboard-error: #ff4757;
  --dashboard-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  --dashboard-glow: 0 0 20px rgba(14, 66, 210, 0.4);
}
