<script setup lang="ts">
import { computed } from 'vue';

defineOptions({
  name: 'DashboardFooter'
});

// 获取当前年份
const currentYear = computed(() => new Date().getFullYear());
</script>

<template>
  <footer
    class="relative h-28px w-full bg-[length:100%_100%] bg-[url('@/assets/bigscreen/footer.png')] bg-center bg-no-repeat"
  >
    <div class="h-full flex items-center justify-center">
      <span class="text-center text-sm text-white/70">© {{ currentYear }} 长沙移动网络中台</span>
    </div>
  </footer>
</template>
