/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/soybeanjs/elegant-router

import type { GeneratedRoute } from '@elegant-router/types';

export const generatedRoutes: GeneratedRoute[] = [
  {
    name: '403',
    path: '/403',
    component: 'layout.blank$view.403',
    meta: {
      title: '403',
      i18nKey: 'route.403',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: '404',
    path: '/404',
    component: 'layout.blank$view.404',
    meta: {
      title: '404',
      i18nKey: 'route.404',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: '500',
    path: '/500',
    component: 'layout.blank$view.500',
    meta: {
      title: '500',
      i18nKey: 'route.500',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: 'dashboard',
    path: '/dashboard',
    component: 'layout.dashboard',
    meta: {
      title: 'dashboard',
      i18nKey: 'route.dashboard',
      icon: 'mdi:monitor-dashboard'
    },
    children: [
      {
        name: 'dashboard_home',
        path: '/dashboard/home',
        component: 'view.dashboard_home',
        meta: {
          title: 'dashboard_home',
          i18nKey: 'route.dashboard_home'
        }
      }
    ]
  },
  {
    name: 'demo',
    path: '/demo',
    component: 'layout.base',
    meta: {
      title: 'demo',
      i18nKey: 'route.demo'
    },
    children: [
      {
        name: 'demo_demo',
        path: '/demo/demo',
        component: 'view.demo_demo',
        meta: {
          title: 'demo_demo',
          i18nKey: 'route.demo_demo'
        }
      },
      {
        name: 'demo_tree',
        path: '/demo/tree',
        component: 'view.demo_tree',
        meta: {
          title: 'demo_tree',
          i18nKey: 'route.demo_tree'
        }
      }
    ]
  },
  {
    name: 'home',
    path: '/home',
    component: 'layout.base$view.home',
    meta: {
      title: 'home',
      i18nKey: 'route.home',
      icon: 'mdi:monitor-dashboard',
      order: 1
    }
  },
  {
    name: 'iframe-page',
    path: '/iframe-page/:url',
    component: 'layout.base$view.iframe-page',
    props: true,
    meta: {
      title: 'iframe-page',
      i18nKey: 'route.iframe-page',
      constant: true,
      hideInMenu: true,
      keepAlive: true
    }
  },
  {
    name: 'login',
    path: '/login/:module(pwd-login|code-login|register|reset-pwd|bind-wechat)?',
    component: 'layout.blank$view.login',
    props: true,
    meta: {
      title: 'login',
      i18nKey: 'route.login',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: 'monitor',
    path: '/monitor',
    component: 'layout.base',
    meta: {
      title: 'monitor',
      i18nKey: 'route.monitor'
    },
    children: [
      {
        name: 'monitor_cache',
        path: '/monitor/cache',
        component: 'view.monitor_cache',
        meta: {
          title: 'monitor_cache',
          i18nKey: 'route.monitor_cache'
        }
      },
      {
        name: 'monitor_logininfor',
        path: '/monitor/logininfor',
        component: 'view.monitor_logininfor',
        meta: {
          title: 'monitor_logininfor',
          i18nKey: 'route.monitor_logininfor'
        }
      },
      {
        name: 'monitor_online',
        path: '/monitor/online',
        component: 'view.monitor_online',
        meta: {
          title: 'monitor_online',
          i18nKey: 'route.monitor_online'
        }
      },
      {
        name: 'monitor_operlog',
        path: '/monitor/operlog',
        component: 'view.monitor_operlog',
        meta: {
          title: 'monitor_operlog',
          i18nKey: 'route.monitor_operlog'
        }
      }
    ]
  },
  {
    name: 'social-callback',
    path: '/social-callback',
    component: 'layout.blank$view.social-callback',
    meta: {
      title: 'social-callback',
      i18nKey: 'route.social-callback',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: 'system',
    path: '/system',
    component: 'layout.base',
    meta: {
      title: 'system',
      i18nKey: 'route.system',
      localIcon: 'menu-system',
      order: 1
    },
    children: [
      {
        name: 'system_client',
        path: '/system/client',
        component: 'view.system_client',
        meta: {
          title: 'system_client',
          i18nKey: 'route.system_client'
        }
      },
      {
        name: 'system_config',
        path: '/system/config',
        component: 'view.system_config',
        meta: {
          title: 'system_config',
          i18nKey: 'route.system_config'
        }
      },
      {
        name: 'system_dept',
        path: '/system/dept',
        component: 'view.system_dept',
        meta: {
          title: 'system_dept',
          i18nKey: 'route.system_dept'
        }
      },
      {
        name: 'system_dict',
        path: '/system/dict',
        component: 'view.system_dict',
        meta: {
          title: 'system_dict',
          i18nKey: 'route.system_dict'
        }
      },
      {
        name: 'system_menu',
        path: '/system/menu',
        component: 'view.system_menu',
        meta: {
          title: 'system_menu',
          i18nKey: 'route.system_menu',
          localIcon: 'menu-tree-table',
          order: 3
        }
      },
      {
        name: 'system_notice',
        path: '/system/notice',
        component: 'view.system_notice',
        meta: {
          title: 'system_notice',
          i18nKey: 'route.system_notice'
        }
      },
      {
        name: 'system_oss',
        path: '/system/oss',
        component: 'view.system_oss',
        meta: {
          title: 'system_oss',
          i18nKey: 'route.system_oss'
        }
      },
      {
        name: 'system_oss-config',
        path: '/system/oss-config',
        component: 'view.system_oss-config',
        meta: {
          title: 'system_oss-config',
          i18nKey: 'route.system_oss-config',
          constant: true,
          hideInMenu: true,
          icon: 'hugeicons:configuration-01'
        }
      },
      {
        name: 'system_post',
        path: '/system/post',
        component: 'view.system_post',
        meta: {
          title: 'system_post',
          i18nKey: 'route.system_post'
        }
      },
      {
        name: 'system_role',
        path: '/system/role',
        component: 'view.system_role',
        meta: {
          title: 'system_role',
          i18nKey: 'route.system_role'
        }
      },
      {
        name: 'system_tenant',
        path: '/system/tenant',
        component: 'view.system_tenant',
        meta: {
          title: 'system_tenant',
          i18nKey: 'route.system_tenant'
        }
      },
      {
        name: 'system_tenant-package',
        path: '/system/tenant-package',
        component: 'view.system_tenant-package',
        meta: {
          title: 'system_tenant-package',
          i18nKey: 'route.system_tenant-package'
        }
      },
      {
        name: 'system_user',
        path: '/system/user',
        component: 'view.system_user',
        meta: {
          title: 'system_user',
          i18nKey: 'route.system_user'
        }
      }
    ]
  },
  {
    name: 'tool',
    path: '/tool',
    component: 'layout.base',
    meta: {
      title: 'tool',
      i18nKey: 'route.tool',
      localIcon: 'menu-tool',
      order: 4
    },
    children: [
      {
        name: 'tool_gen',
        path: '/tool/gen',
        component: 'view.tool_gen',
        meta: {
          title: 'tool_gen',
          i18nKey: 'route.tool_gen',
          localIcon: 'menu-code',
          order: 2
        }
      }
    ]
  },
  {
    name: 'user-center',
    path: '/user-center',
    component: 'layout.base$view.user-center',
    meta: {
      title: 'user-center',
      i18nKey: 'route.user-center',
      icon: 'material-symbols:account-circle-full',
      hideInMenu: true
    }
  }
];
