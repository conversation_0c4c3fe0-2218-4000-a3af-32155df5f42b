<script setup lang="ts">
import { ref, watch } from 'vue';

interface Props {
  visible: boolean;
}

const props = defineProps<Props>();
const emit = defineEmits(['update:visible']);

const dialogVisible = ref(props.visible);

// 监听visible属性变化
watch(
  () => props.visible,
  val => {
    dialogVisible.value = val;
  }
);

// 监听内部visible变化，向父组件发送更新事件
watch(
  () => dialogVisible.value,
  val => {
    emit('update:visible', val);
  }
);

// 关闭对话框
const closeDialog = () => {
  dialogVisible.value = false;
};

// 发送消息
const message = ref('');
const chatMessages = ref([
  { role: 'system', content: '您好，我是AI助手，有什么可以帮助您的？', time: new Date().toLocaleTimeString() }
]);

const sendMessage = () => {
  if (!message.value.trim()) return;

  // 添加用户消息
  chatMessages.value.push({
    role: 'user',
    content: message.value,
    time: new Date().toLocaleTimeString()
  });

  // 模拟AI回复
  setTimeout(() => {
    chatMessages.value.push({
      role: 'system',
      content: '我正在处理您的问题，请稍候...',
      time: new Date().toLocaleTimeString()
    });
  }, 500);

  // 清空输入框
  message.value = '';
};
</script>

<template>
  <NModal
    v-model:show="dialogVisible"
    preset="card"
    title="智能助手"
    class="w-500px"
    :bordered="false"
    :mask-closable="false"
  >
    <div class="h-550px flex flex-col">
      <!-- 对话内容区域 -->
      <div class="flex-1 overflow-auto rounded-8px p-4" :style="{ backgroundColor: '#0E42D2' }">
        <div
          v-for="(msg, index) in chatMessages"
          :key="index"
          class="mb-4"
          :class="msg.role === 'user' ? 'flex flex-row-reverse' : 'flex'"
        >
          <!-- 头像 -->
          <div
            class="mr-2 h-40px w-40px flex flex-shrink-0 items-center justify-center rounded-full"
            :style="{
              backgroundColor: msg.role === 'user' ? '#0E42D2' : '#0E42D2'
            }"
          >
            <SvgIcon
              :icon="msg.role === 'user' ? 'carbon:user-avatar' : 'mdi:robot'"
              class="text-xl"
              :style="{ color: '#0E42D2' }"
            />
          </div>

          <!-- 消息内容 -->
          <div
            class="max-w-[70%] rounded-8px p-3"
            :style="{
              backgroundColor: msg.role === 'user' ? '#0E42D2' : '#0E42D2'
            }"
          >
            <div :style="{ color: '#0E42D2' }">{{ msg.content }}</div>
            <div class="mt-1 text-right text-xs" :style="{ color: '#0E42D2' }">
              {{ msg.time }}
            </div>
          </div>
        </div>
      </div>

      <!-- 输入区域 -->
      <div class="mt-3 h-60px flex items-center">
        <NInput
          v-model:value="message"
          type="text"
          placeholder="请输入您的问题..."
          class="mr-2 flex-1"
          @keyup.enter="sendMessage"
        >
          <template #prefix>
            <NIcon>
              <SvgIcon icon="mdi:message-text" />
            </NIcon>
          </template>
        </NInput>
        <NButton type="primary" @click="sendMessage">
          <template #icon>
            <NIcon>
              <SvgIcon icon="mdi:send" />
            </NIcon>
          </template>
          发送
        </NButton>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-end">
        <NButton @click="closeDialog">关闭</NButton>
      </div>
    </template>
  </NModal>
</template>
