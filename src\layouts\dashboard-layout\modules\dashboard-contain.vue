<script setup lang="ts">
import { onBeforeUnmount, onMounted, ref, watch } from 'vue';

defineOptions({
  name: 'DashboardContain'
});

interface Props {
  /** 设计宽度 */
  designWidth?: number;
  /** 设计高度 */
  designHeight?: number;
  /** 是否启用缩放 */
  enableScale?: boolean;
}

// 组件属性默认值
const props = withDefaults(defineProps<Props>(), {
  designWidth: 1920,
  designHeight: 1080,
  enableScale: true
});

// 大屏容器引用
const containerRef = ref<HTMLElement | null>(null);

/**
 * 计算大屏缩放比例
 * @returns {number} 缩放比例
 */
const getScale = () => {
  const ww = window.innerWidth / props.designWidth;
  const wh = window.innerHeight / props.designHeight;
  return ww < wh ? ww : wh;
};

/**
 * 响应窗口大小变化，重新计算缩放比例
 */
const resize = () => {
  if (containerRef.value) {
    if (props.enableScale) {
      containerRef.value.style.transform = `scale(${getScale()}) translate(-50%, -50%)`;
    } else {
      containerRef.value.style.transform = 'translate(-50%, -50%)';
    }
  }
};

/**
 * 初始化大屏容器尺寸和缩放
 */
onMounted(() => {
  if (containerRef.value) {
    if (props.enableScale) {
      containerRef.value.style.transform = `scale(${getScale()}) translate(-50%, -50%)`;
    } else {
      containerRef.value.style.transform = 'translate(-50%, -50%)';
    }
    containerRef.value.style.width = `${props.designWidth}px`;
    containerRef.value.style.height = `${props.designHeight}px`;
  }
  window.addEventListener('resize', resize);
});

/**
 * 监听enableScale属性变化
 */
watch(
  () => props.enableScale,
  () => {
    resize();
  }
);

/**
 * 组件销毁前移除事件监听
 */
onBeforeUnmount(() => {
  window.removeEventListener('resize', resize);
});
</script>

<template>
  <div class="h-full w-full overflow-hidden bg-[#082761]">
    <div
      ref="containerRef"
      class="fixed left-1/2 top-1/2 z-999 flex flex-col origin-top-left overflow-hidden transition-all duration-300"
    >
      <slot></slot>
    </div>
  </div>
</template>
