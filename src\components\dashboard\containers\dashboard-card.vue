<script setup lang="ts">
/**
 * 标准卡片组件属性
 */
interface Props {
  /**
   * 卡片标题
   */
  title: string;
}

// 定义组件属性
defineProps<Props>();
</script>

<template>
  <div class="normal-card">
    <div class="normal-card-header">
      <div class="normal-card-title">
        <div class="icon"></div>
        <span>{{ title }}</span>
      </div>
      <slot name="button"></slot>
    </div>
    <div class="normal-card-main">
      <slot></slot>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.normal-card {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: rgb(140 189 255 / 4%);
  backdrop-filter: blur(8px);
  .normal-card-header {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 40px;
    padding: 0 15px;
    &::before {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 40px;
      content: '';
      background: url('../images/panelHeader.png') no-repeat;
      background-size: 100% 100%;
    }
    .normal-card-title {
      position: relative;
      z-index: 1;
      display: flex;
      align-items: center;
      padding-left: 5px;
      font-family: YouSheBiaoTiHei;
      font-size: 30px;
      color: #ffffff;
      text-shadow: 0 0 4px #0066ff;

      // cursor: pointer;
      .icon {
        position: relative;
        box-sizing: border-box;
        display: inline-block;
        width: 15px;
        height: 15px;
        margin-right: 15px;
        border: 2px solid #14b4ff;
        &::before {
          position: absolute;
          top: 3px;
          left: 3px;
          display: inline-block;
          width: 5px;
          height: 5px;
          content: '';
          background: #f9b93b;
          box-shadow:
            0 0 8px 1px #0091f8,
            0 0 4px 0 rgb(33 121 195 / 88%);
        }
      }
    }
  }
  .normal-card-main {
    flex: 1;

    // padding: 10px;
    overflow: hidden;
  }
}
</style>
