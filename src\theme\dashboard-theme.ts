import type { GlobalThemeOverrides } from 'naive-ui';

/** 蓝色主题数据大屏专用配置 */
export const dashboardTheme: GlobalThemeOverrides = {
  common: {
    primaryColor: '#1890ff',
    primaryColorHover: '#40a9ff',
    primaryColorPressed: '#0050b3',
    primaryColorSuppl: '#69c0ff',

    // 背景色
    bodyColor: 'transparent',
    cardColor: 'rgba(255, 255, 255, 0.08)',
    modalColor: 'rgba(255, 255, 255, 0.12)',
    popoverColor: 'rgba(255, 255, 255, 0.12)',

    // 文字颜色
    textColorBase: '#ffffff',
    textColor1: '#ffffff',
    textColor2: 'rgba(255, 255, 255, 0.9)',
    textColor3: 'rgba(255, 255, 255, 0.7)',

    // 边框
    borderColor: 'rgba(24, 144, 255, 0.3)',
    dividerColor: 'rgba(24, 144, 255, 0.2)',

    // 阴影
    boxShadow1: '0 8px 32px rgba(0, 0, 0, 0.4)',
    boxShadow2: '0 0 20px rgba(24, 144, 255, 0.4)',
    boxShadow3: '0 12px 40px rgba(24, 144, 255, 0.3)',

    // 圆角
    borderRadius: '12px',
    borderRadiusSmall: '6px',

    // 字体
    fontFamily:
      '"Inter", "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif',
    fontWeight: '400',
    fontWeightStrong: '600'
  },
  Card: {
    color: 'rgba(255, 255, 255, 0.08)',
    colorModal: 'rgba(255, 255, 255, 0.12)',
    colorEmbedded: 'rgba(255, 255, 255, 0.05)',
    borderColor: 'rgba(24, 144, 255, 0.3)',
    borderRadius: '16px',
    paddingMedium: '24px 28px',
    titleFontSizeSmall: '16px',
    titleFontSizeMedium: '18px',
    titleFontSizeLarge: '20px',
    titleTextColor: '#ffffff',
    titleFontWeight: '600'
  },
  DataTable: {
    thColor: 'rgba(255, 255, 255, 0.1)',
    tdColor: 'transparent',
    borderColor: 'rgba(24, 144, 255, 0.2)',
    thTextColor: 'rgba(255, 255, 255, 0.9)',
    tdTextColor: 'rgba(255, 255, 255, 0.8)',
    borderRadius: '12px'
  },
  Button: {
    colorPrimary: '#1890ff',
    colorHoverPrimary: '#40a9ff',
    colorPressedPrimary: '#0050b3',
    borderPrimary: '1px solid #1890ff',
    borderHoverPrimary: '1px solid #40a9ff',
    borderPressedPrimary: '1px solid #0050b3',
    borderRadius: '10px',
    fontWeight: '500',

    // 次要按钮
    colorSecondary: 'rgba(255, 255, 255, 0.1)',
    colorHoverSecondary: 'rgba(255, 255, 255, 0.15)',
    colorPressedSecondary: 'rgba(255, 255, 255, 0.08)',
    borderSecondary: '1px solid rgba(24, 144, 255, 0.3)',

    // 四级按钮
    colorQuaternary: 'transparent',
    colorHoverQuaternary: 'rgba(24, 144, 255, 0.15)',
    colorPressedQuaternary: 'rgba(24, 144, 255, 0.2)'
  },
  Input: {
    color: 'rgba(255, 255, 255, 0.08)',
    colorFocus: 'rgba(255, 255, 255, 0.12)',
    textColor: '#ffffff',
    placeholderColor: 'rgba(255, 255, 255, 0.5)',
    border: '1px solid rgba(24, 144, 255, 0.3)',
    borderHover: '1px solid rgba(24, 144, 255, 0.5)',
    borderFocus: '1px solid #1890ff',
    borderRadius: '10px'
  },
  Select: {
    peers: {
      InternalSelection: {
        color: 'rgba(255, 255, 255, 0.08)',
        colorActive: 'rgba(255, 255, 255, 0.12)',
        textColor: '#ffffff',
        placeholderColor: 'rgba(255, 255, 255, 0.5)',
        border: '1px solid rgba(24, 144, 255, 0.3)',
        borderHover: '1px solid rgba(24, 144, 255, 0.5)',
        borderActive: '1px solid #1890ff',
        borderRadius: '10px'
      }
    }
  },
  Menu: {
    color: 'transparent',
    itemColorHover: 'rgba(24, 144, 255, 0.15)',
    itemColorActive: 'rgba(24, 144, 255, 0.25)',
    itemTextColor: 'rgba(255, 255, 255, 0.8)',
    itemTextColorHover: '#ffffff',
    itemTextColorActive: '#ffffff',
    itemIconColor: 'rgba(255, 255, 255, 0.7)',
    itemIconColorHover: '#ffffff',
    itemIconColorActive: '#ffffff',
    borderRadius: '10px'
  },
  Tooltip: {
    color: 'rgba(0, 21, 41, 0.95)',
    textColor: '#ffffff',
    borderRadius: '8px',
    boxShadow: '0 4px 16px rgba(24, 144, 255, 0.3)'
  },
  Dropdown: {
    color: 'rgba(255, 255, 255, 0.12)',
    optionColorHover: 'rgba(24, 144, 255, 0.15)',
    optionColorActive: 'rgba(24, 144, 255, 0.25)',
    optionTextColor: 'rgba(255, 255, 255, 0.8)',
    optionTextColorHover: '#ffffff',
    optionTextColorActive: '#ffffff',
    borderRadius: '10px',
    boxShadow: '0 8px 32px rgba(0, 0, 0, 0.4)'
  }
};
