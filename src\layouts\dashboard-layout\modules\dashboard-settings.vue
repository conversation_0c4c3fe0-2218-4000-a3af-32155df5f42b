<script setup lang="ts">
import { ref } from 'vue';
import { NButton, NCard, NForm, NFormItem, NIcon, NModal, NSelect, NSlider, NSpace, NSwitch } from 'naive-ui';

defineOptions({
  name: 'DashboardSettings'
});

interface Props {
  visible: boolean;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const settings = ref({
  autoRefresh: true,
  refreshInterval: 30,
  theme: 'dark',
  showGrid: true,
  showParticles: true,
  animationSpeed: 1
});

const themeOptions = [
  { label: '深色主题', value: 'dark' },
  { label: '浅色主题', value: 'light' },
  { label: '自动', value: 'auto' }
];

const handleClose = () => {
  emit('update:visible', false);
};

const handleSave = () => {
  // 保存设置逻辑
  console.log('保存设置:', settings.value);
  handleClose();
};

const handleReset = () => {
  settings.value = {
    autoRefresh: true,
    refreshInterval: 30,
    theme: 'dark',
    showGrid: true,
    showParticles: true,
    animationSpeed: 1
  };
};
</script>

<template>
  <NModal :show="props.visible" @update:show="handleClose">
    <NCard style="width: 600px" title="数据大屏设置" :bordered="false" size="huge" role="dialog" aria-modal="true">
      <template #header-extra>
        <NButton quaternary circle @click="handleClose">
          <template #icon>
            <NIcon>
              <i class="mdi:close"></i>
            </NIcon>
          </template>
        </NButton>
      </template>

      <NForm :model="settings" label-placement="left" label-width="120px">
        <NFormItem label="自动刷新">
          <NSwitch v-model:value="settings.autoRefresh" />
        </NFormItem>

        <NFormItem v-if="settings.autoRefresh" label="刷新间隔">
          <NSlider
            v-model:value="settings.refreshInterval"
            :min="5"
            :max="300"
            :step="5"
            :format-tooltip="value => `${value}秒`"
          />
        </NFormItem>

        <NFormItem label="主题模式">
          <NSelect v-model:value="settings.theme" :options="themeOptions" placeholder="选择主题" />
        </NFormItem>

        <NFormItem label="显示网格">
          <NSwitch v-model:value="settings.showGrid" />
        </NFormItem>

        <NFormItem label="显示粒子">
          <NSwitch v-model:value="settings.showParticles" />
        </NFormItem>

        <NFormItem label="动画速度">
          <NSlider
            v-model:value="settings.animationSpeed"
            :min="0.5"
            :max="2"
            :step="0.1"
            :format-tooltip="value => `${value}x`"
          />
        </NFormItem>
      </NForm>

      <template #footer>
        <NSpace justify="end">
          <NButton @click="handleReset">重置</NButton>
          <NButton @click="handleClose">取消</NButton>
          <NButton type="primary" @click="handleSave">保存</NButton>
        </NSpace>
      </template>
    </NCard>
  </NModal>
</template>

<style scoped>
:deep(.n-card-header) {
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(14, 66, 210, 0.2);
}

:deep(.n-form-item-label) {
  color: rgba(255, 255, 255, 0.8);
}
</style>
