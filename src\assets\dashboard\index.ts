/**
 * 大屏背景图片资源管理
 */

// 导入背景图片
import dashboardBackground from './imgs/dashboard-background.jpg';
import dashboardFooter from './imgs/dashboard-footer.png';
import dashboardHeaderBg from './imgs/dashboard-header-bg.png';
import dashboardLogo from './imgs/dashboard-logo.png';
import dashboardRobot from './imgs/dashboard-robot.png';
import digitalHumanIconBg from './imgs/digital-human-icon-bg.png';
import diskChart from './imgs/disk-chart.svg';
import mapBackground from './imgs/map-background.png';
import mapContentBg from './imgs/map-content-bg.png';
import mapHeaderBg from './imgs/map-header-bg.png';

/**
 * 背景图片配置
 */
export const dashboardAssets = {
  // 主背景图片
  backgrounds: {
    main: dashboardBackground,
    map: mapBackground,
    mapContent: mapContentBg,
    mapHeader: mapHeaderBg
  },

  // 头部相关图片
  header: {
    background: dashboardHeaderBg,
    logo: dashboardLogo
  },

  // 页脚相关图片
  footer: {
    background: dashboardFooter
  },

  // 装饰图片
  decorations: {
    robot: dashboardRobot,
    digitalHumanIcon: digitalHumanIconBg,
    diskChart
  }
};

/**
 * 背景样式配置
 */
export const backgroundStyles = {
  // 主背景样式
  main: {
    backgroundImage: `url(${dashboardBackground})`,
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    backgroundRepeat: 'no-repeat',
    backgroundAttachment: 'fixed'
  },

  // 地图背景样式
  map: {
    backgroundImage: `url(${mapBackground})`,
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    backgroundRepeat: 'no-repeat'
  },

  // 头部背景样式
  header: {
    backgroundImage: `url(${dashboardHeaderBg})`,
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    backgroundRepeat: 'no-repeat'
  },

  // 页脚背景样式
  footer: {
    backgroundImage: `url(${dashboardFooter})`,
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    backgroundRepeat: 'no-repeat'
  },

  // 内容区域背景样式
  content: {
    backgroundImage: `url(${mapContentBg})`,
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    backgroundRepeat: 'no-repeat'
  }
};

/**
 * 获取背景样式
 * @param type 背景类型
 * @returns CSS 样式对象
 */
export function getBackgroundStyle(type: keyof typeof backgroundStyles) {
  return backgroundStyles[type] || backgroundStyles.main;
}

/**
 * 获取背景图片URL
 * @param category 图片分类
 * @param name 图片名称
 * @returns 图片URL
 */
export function getAssetUrl(category: keyof typeof dashboardAssets, name: string): string {
  const categoryAssets = dashboardAssets[category] as Record<string, string>;
  return categoryAssets[name] || '';
}

export default dashboardAssets;
