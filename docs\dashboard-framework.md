# 数据大屏框架使用指南

## 概述

本项目提供了一个完整的数据大屏框架，包含导航区、内容区、页脚，并具有丰富的动态效果。框架基于 Naive UI 组件库和 UnoCSS 构建。

## 功能特性

### 🎨 视觉效果
- **渐变背景**: 深色科技感背景，支持动态网格和粒子效果
- **毛玻璃效果**: 所有卡片和组件都采用毛玻璃背景
- **动画效果**: 页面进入、卡片悬停、数据更新等多种动画
- **响应式设计**: 支持不同屏幕尺寸的自适应布局

### 🧭 导航功能
- **菜单切换**: 支持多个数据大屏页面间的切换
- **全屏控制**: 一键进入/退出全屏模式
- **返回按钮**: 快速返回主页面
- **刷新功能**: 实时刷新数据

### 📊 内容展示
- **数据卡片**: 展示关键指标，支持趋势显示
- **图表区域**: 预留图表展示空间
- **系统监控**: 实时显示系统状态
- **活动日志**: 显示最近的系统活动

### 🔧 页脚信息
- **版权信息**: 显示系统版权和技术栈
- **系统状态**: 实时监控 CPU、内存、网络状态
- **在线用户**: 显示当前在线用户数

## 使用方法

### 1. 访问数据大屏

```
http://localhost:3000/dashboard/home
```

### 2. 自定义数据大屏页面

创建新的数据大屏页面：

```vue
<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { NCard, NGrid, NGi } from 'naive-ui';

// 你的数据逻辑
const data = ref([]);

onMounted(() => {
  // 加载数据
});
</script>

<template>
  <div class="custom-dashboard">
    <!-- 你的内容 -->
  </div>
</template>

<style scoped>
/* 你的样式 */
</style>
```

### 3. 添加新的导航菜单

在 `src/layouts/dashboard-layout/modules/dashboard-navigation.vue` 中修改 `navigationItems`：

```typescript
const navigationItems = ref([
  { key: 'overview', label: '总览', icon: 'mdi:view-dashboard' },
  { key: 'analytics', label: '分析', icon: 'mdi:chart-line' },
  { key: 'monitor', label: '监控', icon: 'mdi:monitor' },
  { key: 'reports', label: '报表', icon: 'mdi:file-chart' },
  { key: 'custom', label: '自定义', icon: 'mdi:cog' } // 新增
]);
```

### 4. 自定义主题

修改 `src/theme/dashboard-theme.ts` 中的主题配置：

```typescript
export const dashboardTheme: GlobalThemeOverrides = {
  common: {
    primaryColor: '#your-color', // 修改主色调
    // 其他配置...
  }
};
```

## 组件结构

```
src/layouts/dashboard-layout/
├── index.vue                    # 主布局组件
└── modules/
    ├── dashboard-navigation.vue # 导航组件
    └── dashboard-footer.vue     # 页脚组件

src/views/dashboard/
└── home/
    └── index.vue               # 示例数据大屏页面

src/theme/
└── dashboard-theme.ts          # 数据大屏主题配置
```

## 动画效果

### 页面进入动画
- 整体页面：淡入 + 缩放
- 头部：从上滑入
- 导航：从上滑入（延迟）
- 内容：从下淡入（延迟）
- 页脚：从下滑入（延迟）

### 交互动画
- 卡片悬停：上移 + 阴影增强
- 按钮悬停：颜色变化 + 上移
- 数据更新：数字滚动动画

### 背景动画
- 网格移动：无限循环移动
- 粒子浮动：从下到上浮动

## 最佳实践

### 1. 性能优化
- 使用 `v-show` 而不是 `v-if` 来切换显示状态
- 大数据量时使用虚拟滚动
- 图表组件按需加载

### 2. 数据更新
- 使用 WebSocket 进行实时数据推送
- 实现数据缓存机制
- 错误处理和重试机制

### 3. 响应式设计
- 使用 Naive UI 的栅格系统
- 设置合理的断点
- 移动端优化

## 技术栈

- **Vue 3**: 前端框架
- **Naive UI**: 组件库
- **UnoCSS**: 原子化 CSS
- **TypeScript**: 类型支持
- **Vite**: 构建工具

## 浏览器支持

- Chrome >= 88
- Firefox >= 78
- Safari >= 14
- Edge >= 88

## 更新日志

### v1.0.0
- ✨ 初始版本发布
- 🎨 完整的数据大屏框架
- 🚀 丰富的动画效果
- 📱 响应式设计支持
